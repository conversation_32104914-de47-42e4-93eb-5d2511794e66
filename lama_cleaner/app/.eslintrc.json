{"extends": ["airbnb", "airbnb/hooks", "plugin:@typescript-eslint/recommended", "prettier", "plugin:prettier/recommended"], "plugins": ["@typescript-eslint", "react", "react-hooks", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 2018, "sourceType": "module", "project": "./tsconfig.json"}, "rules": {"jsx-a11y/click-events-have-key-events": 0, "react/jsx-props-no-spreading": 0, "import/no-unresolved": 0, "react/jsx-no-bind": "off", "react/jsx-filename-extension": [1, {"extensions": [".ts", ".tsx"]}], "prettier/prettier": ["error", {"singleQuote": true, "arrowParens": "avoid", "endOfLine": "auto"}], "consistent-return": "off", "no-use-before-define": "off", "import/extensions": "off", "react/prop-types": 0, "react/require-default-props": "off", "no-shadow": "off", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-shadow": ["error"], "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn"}}