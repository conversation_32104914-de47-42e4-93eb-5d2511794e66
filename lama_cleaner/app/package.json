{"name": "lama-cleaner", "version": "0.1.0", "private": true, "proxy": "http://127.0.0.1:8080", "dependencies": {"@babel/core": "^7.16.0", "@heroicons/react": "^2.0.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.3", "@radix-ui/colors": "^0.1.8", "@radix-ui/react-dialog": "0.1.8-rc.25", "@radix-ui/react-dropdown-menu": "^2.0.4", "@radix-ui/react-icons": "^1.1.1", "@radix-ui/react-popover": "^1.0.0", "@radix-ui/react-progress": "^1.0.2", "@radix-ui/react-scroll-area": "^1.0.2", "@radix-ui/react-select": "0.1.2-rc.27", "@radix-ui/react-switch": "^0.1.5", "@radix-ui/react-tabs": "^1.0.1", "@radix-ui/react-toast": "^0.1.1", "@radix-ui/react-tooltip": "^0.1.7", "@svgr/webpack": "^5.5.0", "@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^12.1.2", "@testing-library/user-event": "^13.5.0", "@types/flexsearch": "^0.7.3", "@types/jest": "^27.0.2", "@types/lodash": "^4.14.182", "@types/node": "^16.11.1", "@types/react": "^17.0.30", "@types/react-dom": "^17.0.9", "babel-jest": "^27.4.2", "babel-loader": "^8.2.3", "babel-plugin-named-asset-import": "^0.3.8", "babel-preset-react-app": "^10.0.1", "bfj": "^7.0.2", "browserslist": "^4.18.1", "camelcase": "^6.2.1", "case-sensitive-paths-webpack-plugin": "^2.4.0", "cross-env": "7.x", "css-loader": "^6.5.1", "css-minimizer-webpack-plugin": "^3.2.0", "dotenv": "^10.0.0", "dotenv-expand": "^5.1.0", "eslint": "^8.3.0", "eslint-config-react-app": "^7.0.1", "eslint-webpack-plugin": "^3.1.1", "file-loader": "^6.2.0", "flexsearch": "0.7.21", "fs-extra": "^10.0.0", "hacktimer": "^1.1.3", "html-webpack-plugin": "^5.5.0", "identity-obj-proxy": "^3.0.0", "inter-ui": "^3.19.3", "jest": "^27.4.3", "jest-resolve": "^27.4.2", "jest-watch-typeahead": "^1.0.0", "lodash": "^4.17.21", "mini-css-extract-plugin": "^2.4.5", "mitt": "^3.0.0", "nanoid": "^4.0.0", "npm-run-all": "4.x", "postcss": "^8.4.4", "postcss-flexbugs-fixes": "^5.0.2", "postcss-loader": "^6.2.1", "postcss-normalize": "^10.0.1", "postcss-preset-env": "^7.0.1", "prompts": "^2.4.2", "react": "^17.0.2", "react-app-polyfill": "^3.0.0", "react-dev-utils": "^12.0.1", "react-dom": "^17.0.2", "react-feather": "^2.0.10", "react-hotkeys-hook": "^3.4.7", "react-photo-album": "^2.0.0", "react-refresh": "^0.11.0", "react-use": "^17.3.1", "react-zoom-pan-pinch": "^2.1.3", "recoil": "^0.6.1", "resolve": "^1.20.0", "resolve-url-loader": "^4.0.0", "sass-loader": "^12.3.0", "semver": "^7.3.5", "socket.io-client": "^4.5.4", "source-map-loader": "^3.0.0", "style-loader": "^3.3.1", "tailwindcss": "^3.0.2", "terser-webpack-plugin": "^5.2.5", "typescript": "4.x", "webpack": "^5.64.4", "webpack-dev-server": "^4.6.0", "webpack-manifest-plugin": "^4.0.2", "workbox-webpack-plugin": "^6.4.1"}, "scripts": {"start": "cross-env GENERATE_SOURCEMAP=false node scripts/start.js", "build": "cross-env GENERATE_SOURCEMAP=false node scripts/build.js", "test": "node scripts/test.js"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.1.0", "eslint-config-airbnb": "^18.2.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.25.2", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.27.1", "eslint-plugin-react-hooks": "^4.3.0", "prettier": "^2.4.1", "sass": "^1.49.9"}, "jest": {"roots": ["<rootDir>/src"], "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts"], "setupFiles": ["react-app-polyfill/jsdom"], "setupFilesAfterEnv": ["<rootDir>/src/setupTests.ts"], "testMatch": ["<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}", "<rootDir>/src/**/*.{spec,test}.{js,jsx,ts,tsx}"], "testEnvironment": "jsdom", "transform": {"^.+\\.(js|jsx|mjs|cjs|ts|tsx)$": "<rootDir>/config/jest/babelTransform.js", "^.+\\.css$": "<rootDir>/config/jest/cssTransform.js", "^(?!.*\\.(js|jsx|mjs|cjs|ts|tsx|css|json)$)": "<rootDir>/config/jest/fileTransform.js"}, "transformIgnorePatterns": ["[/\\\\]node_modules[/\\\\].+\\.(js|jsx|mjs|cjs|ts|tsx)$", "^.+\\.module\\.(css|sass|scss)$"], "modulePaths": [], "moduleNameMapper": {"^react-native$": "react-native-web", "^.+\\.module\\.(css|sass|scss)$": "identity-obj-proxy"}, "moduleFileExtensions": ["web.js", "js", "web.ts", "ts", "web.tsx", "tsx", "json", "web.jsx", "jsx", "node"], "watchPlugins": ["jest-watch-typeahead/filename", "jest-watch-typeahead/testname"], "resetMocks": true}, "babel": {"presets": ["react-app"]}}